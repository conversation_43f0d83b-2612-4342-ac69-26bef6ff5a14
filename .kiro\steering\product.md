# Product Overview

This is a professional camera control SDK for Android devices based on RK3588 chipset, designed for high-definition image capture, video recording, and real-time streaming applications.

## Core Product

**TP2HD-VisionSDK** - A comprehensive camera control and video processing SDK that provides:

- High-quality 4K video recording and image capture
- Real-time RTSP streaming capabilities  
- Network file sharing via Samba/SMB protocol
- Hardware ISP parameter control through serial communication
- ROI (Region of Interest) functionality for advanced exposure control

## Target Use Cases

- Industrial camera applications
- Security and surveillance systems
- Professional video recording equipment
- Remote monitoring and streaming solutions
- Touch screen camera control interfaces

## Key Features

- **Camera Control**: Full Camera2 API integration with hardware acceleration
- **Video Processing**: H.264 encoding with MediaCodec for 4K recording
- **Network Integration**: RTSP streaming and Samba file upload
- **Hardware Communication**: Serial port control for ISP parameters
- **User Interface**: Touch-based ROI selection and gesture controls

## Architecture

The product follows a modular SDK design where:
- **CodecUtils** contains the core SDK implementation
- **VideoTest** serves as both testing platform and reference implementation
- **XCamView** demonstrates real-world application integration
- **touptek_serial_rk** handles low-level hardware communication