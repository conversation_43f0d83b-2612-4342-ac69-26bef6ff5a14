# TP2HD-VisionSDK 项目

基于RK3588芯片的Android触摸屏摄像头控制系统，用于高清图像采集、视频录制和实时通信。

## 快速开始

1. 使用Android Studio导入项目
2. 构建CodecUtils模块生成AAR库
3. 构建并运行app模块进行功能测试

## 项目文档

**重要**: 查看[项目详细指南](PROJECT_GUIDE.md)获取完整的开发文档、架构说明和API参考。

该指南包含：
- 项目架构和模块说明
- 核心API使用示例
- 设计模式和编码规范
- 集成指南和最佳实践
- 常见问题解决方案

## 主要功能

- 高性能视频编码 (H.264/AVC)，支持4K分辨率
- 图像捕获和处理
- RTSP流媒体服务
- ROI (感兴趣区域) 控制
- Samba图片和视频自动上传
- HDMI接口管理

## 依赖关系

- JCIFS-NG: 用于Samba文件上传
- RootEncoder: 用于视频编码和推流
- RTSP-Server: 提供RTSP服务器功能

## 许可证

版权所有 © 2023 