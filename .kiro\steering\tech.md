# Technology Stack

## Build System
- **Gradle** with Android Gradle Plugin
- **Android Studio** as primary IDE
- **<PERSON>ven publishing** for AAR distribution

## Core Technologies

### Android Platform
- **Minimum SDK**: API 30 (Android 11)
- **Target SDK**: API 35 (Android 14)
- **Compile SDK**: API 35
- **Java Version**: 1.8 (source/target compatibility)

### Camera & Video Processing
- **Camera2 API** - Low-level camera control and configuration
- **MediaCodec** - Hardware-accelerated H.264 video encoding
- **MediaMuxer** - MP4 container creation
- **ImageReader** - High-resolution image capture
- **TextureView/SurfaceView** - Video preview rendering

### Network & Communication
- **RootEncoder (2.3.5)** - Video streaming and encoding
- **RTSP-Server (1.2.1)** - Real-time streaming protocol
- **jcifs-ng (2.1.9)** - SMB/CIFS file sharing protocol
- **Socket communication** - Hardware parameter control

### Hardware Integration
- **J<PERSON> (Java Native Interface)** - C++ integration for serial communication
- **Serial port communication** - ISP parameter control
- **RK3588 chipset** - Hardware-specific optimizations

### UI & Image Processing
- **Glide (4.15.1)** - Image loading and caching
- **Android-TiffBitmapFactory (*******)** - TIFF format support
- **Custom gesture handling** - Touch controls and ROI interaction
- **Canvas drawing** - ROI visualization

## Key Libraries & Dependencies

```gradle
// Video streaming and encoding
implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")
implementation("com.github.pedroSG94:RTSP-Server:1.2.1")

// Network file sharing
implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'

// Image processing
implementation 'com.github.bumptech.glide:glide:4.15.1'
implementation 'io.github.beyka:Android-TiffBitmapFactory:*******'

// UI components
implementation 'androidx.recyclerview:recyclerview:1.3.0'
implementation 'androidx.cardview:cardview:1.0.0'
```

## Common Build Commands

### Building the SDK
```bash
# Build CodecUtils AAR library
./gradlew :CodecUtils:assembleRelease

# Build test application
./gradlew :app:assembleDebug

# Clean and rebuild
./gradlew clean build
```

### Testing & Debugging
```bash
# Install debug APK
./gradlew installDebug

# Run unit tests
./gradlew test

# Generate JavaDoc
./gradlew javadoc
```

### Publishing
```bash
# Publish AAR to local repository
./gradlew publishToMavenLocal

# Generate release AAR
./gradlew bundleReleaseAar
```

## Development Environment Setup

1. **Android Studio** - Latest stable version
2. **Android SDK** - API levels 30-35 installed
3. **NDK** - For JNI/C++ components (if modifying native code)
4. **Platform signing** - Uses platform.keystore for system-level permissions

## Performance Considerations

- **Hardware acceleration** - Leverages RK3588 video encoding capabilities
- **60fps target** - Fixed frame rate for consistent performance
- **4K resolution** - Optimized for high-definition recording
- **Memory management** - Careful resource cleanup to prevent leaks
- **Threading** - Background processing for file operations and network uploads