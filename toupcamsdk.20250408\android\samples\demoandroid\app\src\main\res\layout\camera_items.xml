<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/item_foreground">
        <TextView
            android:id="@+id/txt_camname"
            android:layout_width="@dimen/camitem_width"
            android:layout_height="@dimen/camitem_width"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/str_null"
            android:textColor="@color/black"
            android:textStyle="bold" />
</RelativeLayout>
